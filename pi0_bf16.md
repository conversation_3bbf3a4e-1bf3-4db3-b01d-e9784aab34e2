# PI0 BFloat16 Data Type Fix

## Problem Description

When training PI0 policy, you may encounter the following error:

```
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (CUDABFloat16Type) should be the same
```

This error occurs in the vision tower's patch embedding layer during image processing.

## Root Cause

The issue is caused by a data type mismatch:
- **Input images**: Loaded as `float32` from the dataset
- **Model weights**: Converted to `bfloat16` by the `to_bfloat16_like_physical_intelligence()` method
- **Conflict**: PyTorch requires input and weight tensors to have the same data type

## Solution

### Files to Modify

You need to modify the `embed_image` method and `to_bfloat16_like_physical_intelligence` method in the following files:

1. `lerobot/common/policies/pi0/paligemma_with_expert.py`
2. `lerobot_dual/lerobot/common/policies/pi0/paligemma_with_expert.py`
3. `/data/lerobot_dual_pi0/lerobot_dual/lerobot/common/policies/pi0/paligemma_with_expert.py`
4. `/data/lerobot_dual_pi0/lerobot/common/policies/pi0/paligemma_with_expert.py`

### Fix 1: Update embed_image method

Replace the `embed_image` method:

```python
def embed_image(self, image: torch.Tensor):
    return self.paligemma.get_image_features(image)
```

With:

```python
def embed_image(self, image: torch.Tensor):
    # Convert input image to bfloat16 to match model weights
    image = image.to(dtype=torch.bfloat16)
    return self.paligemma.get_image_features(image)
```

### Fix 2: Update to_bfloat16_like_physical_intelligence method

Replace the `to_bfloat16_like_physical_intelligence` method:

```python
def to_bfloat16_like_physical_intelligence(self):
    self.paligemma = self.paligemma.to(dtype=torch.bfloat16)

    params_to_change_dtype = [
        "language_model.model.layers",
        "gemma_expert.model.layers",
        "vision_tower",
        "multi_modal",
    ]
    for name, param in self.named_parameters():
        if any(selector in name for selector in params_to_change_dtype):
            param.data = param.data.to(dtype=torch.bfloat16)
```

With:

```python
def to_bfloat16_like_physical_intelligence(self):
    # Convert the entire paligemma model to bfloat16
    self.paligemma = self.paligemma.to(dtype=torch.bfloat16)
    
    # Explicitly convert vision tower to ensure all components are bfloat16
    if hasattr(self.paligemma, 'vision_tower'):
        self.paligemma.vision_tower = self.paligemma.vision_tower.to(dtype=torch.bfloat16)
    
    # Explicitly convert multi-modal projector
    if hasattr(self.paligemma, 'multi_modal_projector'):
        self.paligemma.multi_modal_projector = self.paligemma.multi_modal_projector.to(dtype=torch.bfloat16)

    # Additional explicit conversion for all parameters to ensure consistency
    params_to_change_dtype = [
        "language_model.model.layers",
        "gemma_expert.model.layers",
        "vision_tower",
        "multi_modal",
        "paligemma.vision_tower",
        "paligemma.multi_modal_projector",
    ]
    for name, param in self.named_parameters():
        if any(selector in name for selector in params_to_change_dtype):
            param.data = param.data.to(dtype=torch.bfloat16)
```

### Clear Python Cache

After making changes, clear Python bytecode cache:

```bash
find /data -name "*.pyc" -delete 2>/dev/null || true
find /data -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
```

## Quick Fix Prompt

If you encounter this error again, use this prompt:

```
I'm getting a "RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (CUDABFloat16Type) should be the same" error when training PI0 policy. 

Please:
1. Find all `paligemma_with_expert.py` files in the codebase
2. In each file, modify the `embed_image` method to convert input images to bfloat16 before processing
3. Update the `to_bfloat16_like_physical_intelligence` method to ensure all vision tower components are properly converted to bfloat16
4. Clear Python cache files

The error occurs because input images are float32 but model weights are bfloat16. The fix ensures data type consistency.
```

## Verification

After applying the fix, the training should start without the data type mismatch error. You should see the normal training logs without the RuntimeError.

## Notes

- This fix ensures that input images are converted to the same data type as the model weights
- The explicit conversion of vision tower components provides additional safety
- Multiple file locations need to be updated due to the project structure with multiple copies of the codebase
- Always clear Python cache after making changes to ensure the new code is loaded
